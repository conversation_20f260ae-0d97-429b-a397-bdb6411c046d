# 基础镜像
FROM lizhi.tencentcloudcr.com/lzwk/gunicorn:22.0.0-py3.11.9-slim-bullseye
LABEL maintainer="<PERSON> <<EMAIL>>"
RUN sed -i 's%mirrors.aliyun.com%mirrors.tencent.com%' /etc/apt/sources.list 

# 设置时区为东八区（Asia/Shanghai）
RUN apt-get update && apt-get install -y tzdata && \
    ln -sf /usr/share/zoneinfo/Asia/Shanghai /etc/localtime && \
    echo 'Asia/Shanghai' > /etc/timezone && \
    apt-get clean

ADD . /app

# 设置工作目录
WORKDIR /app

# 复制项目代码
COPY . .

# 安装依赖
RUN  pip install --upgrade pip -i https://pypi.tuna.tsinghua.edu.cn/simple \
     && pip install -r requirements.txt -i  https://pypi:<EMAIL>/ --trusted-host pypi.weike.fm

# 拷贝环境变量文件（如有）
COPY .env .env

# 暴露FastAPI默认端口
EXPOSE 8000

# 启动命令（可根据实际入口调整）
CMD ["uvicorn", "app.main:app", "--host", "0.0.0.0", "--port", "8000", "--env-file", "/app/.env"]