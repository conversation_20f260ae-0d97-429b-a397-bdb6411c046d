
# 慢API性能监控平台

基于 FastAPI + APScheduler + MySQL + 腾讯云CLS 的高性能慢接口监控平台，支持自动定时采集、聚合、报告生成、企业微信推送，支持Docker一键部署。

## 主要特性
- **分层清晰**：API、服务、模型、定时任务、配置分离，易维护
- **定时任务自动化**：支持分钟/天/周定时采集、聚合、报告、推送
- **企业微信自动推送**：每周一自动推送慢API周报到微信群
- **腾讯云CLS重试机制**：查询CLS日志自动重试，提升稳定性
- **Docker一键部署**：支持本地和服务器快速部署
- **自动建表/迁移**：项目启动自动建表

---

## 快速开始

### 1. 本地开发环境

```bash
# 安装依赖
pip install -r requirements.txt

# 启动服务
uvicorn app.main:app --reload
```

访问健康检查接口：
```
GET http://127.0.0.1:8000/ping
```

### 2. Docker 部署

```bash
# 构建镜像
docker build -t perf-monitor .

# 运行容器（需提前准备.env和MySQL）
docker run -d -p 8000:8000 perf-monitor
```

---

## 目录结构
```
app/
├── main.py              # FastAPI 应用入口，自动建表+定时任务
├── api/v1/              # 路由模块（如monitor.py, metric.py）
├── core/                # 配置(config.py)、定时任务(scheduler.py)
├── models/              # Pydantic/ORM数据模型
├── services/            # 业务逻辑层（如monitor_service.py）
    ├─── source_collect/  # 数据采集模块（如cls_online_users_collect.py）
    ├─── metric_calc/     # 指标计算模块（如cls_slow_api_and_online_calc.py）
    ├─── utils.py           # 工具函数模块（如utils.py）
├── integrations/        # 第三方集成模块（如CLS客户端）
    ├─── tencent_cls/     # 腾讯云CLS客户端
    ├─── prometheus/      # Prometheus客户端
    ├─── utils.py          # 通用工具函数
├── deps/                # 依赖项（如数据库db.py）
├── ...
requirements.txt         # 依赖包列表
Dockerfile               # Docker部署文件
.env                     # 环境变量（需自备）
README.md                # 项目说明
```

---

## 主要环境变量说明（.env）
| 变量名         | 说明             | 示例值                |
| -------------- | ---------------- | --------------------- |
| MYSQL_USER     | MySQL用户名      | root                  |
| MYSQL_PASSWORD | MySQL密码        | yourpassword          |
| MYSQL_HOST     | MySQL主机        | 127.0.0.1             |
| MYSQL_PORT     | MySQL端口        | 3306                  |
| MYSQL_DB       | 数据库名         | wk_perf_monitor       |
| SECRET_ID      | 腾讯云API密钥ID  | xxx                   |
| SECRET_KEY     | 腾讯云API密钥KEY | xxx                   |
| REGION         | 腾讯云CLS区域    | ap-guangzhou          |
| TOPIC_ID       | 腾讯云CLS Topic  | xxx                   |

---

## 主要API说明
- `/api/v1/metric/execute`  指标采集（支持定时/手动）
- `/api/v1/slow_api_agg/aggregate`  慢API明细聚合
- `/api/v1/slow_api_report/generate`  生成慢API性能报告
- `/api/v1/slow_api_report/detail`    获取报告明细（支持推送）

---

## 定时任务说明（详见app/core/config.py）
- 每天00:30 采集metric_id=1（T+1）
- 每天00:40 采集metric_id=2（T+1）
- 每天01:00 聚合慢API
- 每周一08:00 生成慢API报告
- 每周一09:10 推送慢API报告到企业微信群
- 所有定时任务时间点、webhook、CLS重试参数均可在`app/core/config.py`中配置

---

## 腾讯云CLS重试机制
- 所有CLS日志查询自动重试，最大重试次数和间隔可在`app/core/config.py`中配置
- 默认3次，每次间隔3秒

---

## 手动推送/测试
- 可通过 `python -m tests.manual_push` 手动触发企业微信推送，便于调试推送样式

---

## 依赖管理
- 安装依赖：`pip install -r requirements.txt`
- 锁定依赖：`pip freeze > requirements.lock`
- 推荐使用虚拟环境或Docker部署，保证一致性

---

## 其它说明
- 项目启动自动建表/迁移，无需手动操作
- 支持自定义定时任务、采集逻辑、推送内容
- 代码结构清晰，注释完善，便于二次开发


## 生产环境已部署
IP: ***********