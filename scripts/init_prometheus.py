from app.models.monitor import PromClusterORM, PromClusterServiceORM
from app.models.metric_calc import MetricCalcORM

import json


def init_prometheus_cluster_data(db):
    """
    初始化prom_cluster和prom_cluster_service表基础数据，若表为空则插入预设内容
    """
    # 检查集群表是否有数据
    if db.query(PromClusterORM).first():
        return
    # 初始化数据
    cluster = "cls-341jhzsi"
    interval = "5m"
    special_suffixes = ["tos", "toh"]
    namespaces = {"main-weike": "main-weike", "wk-api-gateway": "core", "wk-topic": "iprod", "wk-tic": "core",
                  "wk-script": "iprod", "wk-ms-gateway": "core", "wk-content-manager": "production",
                  "wk-config": "production",
                  "wk-classroom-barrage": "production", "wk-business-lihua": "iprod",
                  "wk-classroom-message": "production",
                  "wk-barrage": "iprod"}
    base_data = {
        "main-weike": [
            {"app": "main-weike", "cpu_limit": "2C", "memory_limit": "6Gi", "cpu_request": "1C",
             "memory_request": "3Gi", "base_pod": "15", "expansion_pod": "20"},
            {"app": "main-weike-tos", "cpu_limit": "2C", "memory_limit": "6Gi", "cpu_request": "1C",
             "memory_request": "3Gi", "base_pod": "15", "expansion_pod": "40"},
            {"app": "main-weike-toh", "cpu_limit": "2C", "memory_limit": "6Gi", "cpu_request": "1C",
             "memory_request": "3Gi", "base_pod": "5", "expansion_pod": ""}
        ],
        "wk-topic": [
            {"app": "wk-topic", "cpu_limit": "2C", "memory_limit": "2Gi", "cpu_request": "500m",
             "memory_request": "700Mi", "base_pod": "20", "expansion_pod": "10"}
        ],
        "wk-tic": [
            {"app": "wk-tic", "cpu_limit": "2C", "memory_limit": "4Gi", "cpu_request": "1C", "memory_request": "1800Mi",
             "base_pod": "20", "expansion_pod": "50"},
            {"app": "wk-tic-tos", "cpu_limit": "2C", "memory_limit": "4Gi", "cpu_request": "600m",
             "memory_request": "1800Mi", "base_pod": "20", "expansion_pod": "30"}
        ],
        "wk-script": [
            {"app": "wk-script", "cpu_limit": "1C", "memory_limit": "1Gi", "cpu_request": "100m",
             "memory_request": "512Mi", "base_pod": "2", "expansion_pod": ""}
        ],
        "wk-ms-gateway": [
            {"app": "wk-ms-gateway", "cpu_limit": "1C", "memory_limit": "2Gi", "cpu_request": "500m",
             "memory_request": "1Gi", "base_pod": "15", "expansion_pod": "20"}
        ],
        "wk-content-manager": [
            {"app": "wk-content-manager", "cpu_limit": "4C", "memory_limit": "4Gi", "cpu_request": "2C",
             "memory_request": "2700Mi", "base_pod": "20", "expansion_pod": "60"}
        ],
        "wk-config": [
            {"app": "wk-config", "cpu_limit": "1C", "memory_limit": "1Gi", "cpu_request": "100m",
             "memory_request": "400Mi", "base_pod": "25", "expansion_pod": "30"}
        ],
        "wk-classroom-message": [
            {"app": "wk-classroom-message", "cpu_limit": "0.5C", "memory_limit": "1Gi", "cpu_request": "50m",
             "memory_request": "160Mi", "base_pod": "25", "expansion_pod": "25"}
        ],
        "wk-classroom-barrage": [
            {"app": "wk-classroom-barrage", "cpu_limit": "1C", "memory_limit": "1Gi", "cpu_request": "50m",
             "memory_request": "350Mi", "base_pod": "25", "expansion_pod": "15"}
        ],
        "wk-business-lihua": [
            {"app": "wk-business-lihua", "cpu_limit": "2500m", "memory_limit": "4Gi", "cpu_request": "1500m",
             "memory_request": "2Gi", "base_pod": "30", "expansion_pod": "20"}
        ],
        "wk-barrage": [
            {"app": "wk-barrage", "cpu_limit": "2C", "memory_limit": "2Gi", "cpu_request": "100m",
             "memory_request": "512Mi", "base_pod": "15", "expansion_pod": ""}
        ],
        "wk-api-gateway": [
            {"app": "wk-api-gateway", "cpu_limit": "2C", "memory_limit": "5Gi", "cpu_request": "500m",
             "memory_request": "2560Mi", "base_pod": "30", "expansion_pod": "50"}
        ]
    }
    # 插入集群
    cluster_obj = PromClusterORM(
        cluster=cluster,
        interval=interval,
        special_suffixes=json.dumps(special_suffixes)
    )
    db.add(cluster_obj)
    db.commit()
    db.refresh(cluster_obj)
    # 插入服务
    for service, apps in base_data.items():
        namespace = namespaces.get(service, "default")
        for item in apps:
            db.add(PromClusterServiceORM(
                cluster_id=cluster_obj.id,
                service=service,
                app=item["app"],
                namespace=namespace,
                cpu_limit=str(item["cpu_limit"]),
                memory_limit=str(item["memory_limit"]),
                cpu_request=str(item["cpu_request"]),
                memory_request=str(item["memory_request"]),
                base_pod=str(item["base_pod"]),
                expansion_pod=str(item["expansion_pod"]) if item["expansion_pod"] else None
            ))
    db.commit()


def init_metric_calc_data(db):
    """
    初始化metric_calc表的默认数据，含name字段（中文名）和rule_json
    """
    metric_rule_json_map = {
        "cpu_avg": {
            "status_rules": [
                [0.0, 0.3, "非常健康", 100],
                [0.3, 0.5, "健康", 90],
                [0.5, 0.7, "轻度风险（关注趋势）", 80],
                [0.7, 0.9, "高风险（建议扩容）", 60],
                [0.9, 999, "紧急（立即扩容）", 30]
            ],
            "thresholds": {
                "非常健康": "< 30%",
                "健康": "30%~50%",
                "轻度风险": "50%~70%",
                "高风险": "70%~90%",
                "紧急": ">=90%"
            }
        },
        "cpu_p95": {
            "status_rules": [
                [0.0, 0.7, "非常健康", 100],
                [0.7, 0.8, "健康", 80],
                [0.8, 0.9, "轻度风险", 60],
                [0.9, 0.95, "高风险", 40],
                [0.95, 999, "紧急扩容", 0]
            ],
            "score_rules": {
                "非常健康": 100,
                "健康": 80,
                "轻度风险": 60,
                "高风险": 40,
                "紧急扩容": 0
            }
        },
        "cpu_peak": {
            "status_rules": [
                [0.0, 0.7, "非常健康", 100],
                [0.7, 0.8, "健康", 80],
                [0.8, 0.9, "轻度风险", 60],
                [0.9, 0.95, "高风险", 40],
                [0.95, 999, "紧急扩容", 0]
            ],
            "score_rules": {
                "非常健康": 100,
                "健康": 80,
                "轻度风险": 60,
                "高风险": 40,
                "紧急扩容": 0
            }
        },
        "cpu_slope": {
            "score_rule": {
                "紧急: 快速上升": 0,
                "关注: 波动上升": 60,
                "预警: 缓慢上升": 80,
                "健康: 资源释放": 100,
                "正常: 趋势平稳": 100,
                "正常: 噪声波动": 90,
                "数据不足": 0
            },
            "thresholds": {
                "significance_p": 0.05,
                "significance_r2": 0.7,
                "steep_slope": 0.5,
                "mild_slope": 0.0
            },
            "level_desc": {
                "紧急: 快速上升": "斜率 > 0.5 且 p < 0.05 且 R² ≥ 0.7，趋势显著快速上升，需紧急关注",
                "关注: 波动上升": "斜率 > 0.5 但未达显著性，存在波动上升，建议关注",
                "预警: 缓慢上升": "0 < 斜率 ≤ 0.5 且显著，缓慢上升，建议预警",
                "健康: 资源释放": "斜率 < 0，资源释放，健康",
                "正常: 趋势平稳": "斜率≈0，趋势平稳",
                "正常: 噪声波动": "斜率>0但不显著，属于噪声波动",
                "数据不足": "数据点不足，无法判断"
            },
            "analysis_note": {
                "significance_threshold": "p<0.05且R²≥0.7视为显著趋势",
                "time_window": "30分钟滑动窗口检测最陡时段",
                "desc": "斜率反映高峰期内Pod CPU利用率的变化趋势，显著上升需关注资源瓶颈或突发流量。"
            }
        },
        "cpu_std": {
            "score_rule": {
                "非常稳定": 100,
                "健康": 80,
                "波动剧烈": 0,
                "一般波动": 60,
                "数据点不足": 0
            },
            "threshold_note": {
                "stable": "<0.100",
                "volatile": ">0.400"
            },
            "level_desc": {
                "非常稳定": "标准差<5% limit",
                "健康": "标准差<20% limit",
                "波动剧烈": "标准差>20% limit",
                "一般波动": "标准差介于5%~20%之间",
                "数据点不足": "采样点数过少"
            }
        },
        "cpu_utilization_p95": {
            "score_rule": {
                "资源充足": 100,
                "可持续观测": 60,
                "风险较高": 0,
                "未知(无Limit配置)": 0
            },
            "thresholds": {
                "enough": "< 0.7",
                "observe": "0.7 ~ 0.9",
                "risk": ">= 0.9"
            },
            "level_desc": {
                "资源充足": "P95利用率<0.7",
                "可持续观测": "0.7≤P95利用率<0.9",
                "风险较高": "P95利用率≥0.9",
                "未知(无Limit配置)": "无limit配置，无法判断"
            }
        },
        "cpu_utilization_peak": {
            "score_rule": {
                "资源充足": 100,
                "临近阈值": 60,
                "有压迫": 0,
                "未知(无Limit配置)": 0
            },
            "thresholds": {
                "safe": "< 0.85",
                "alert": "0.85 ~ 0.93",
                "risk": ">= 0.93"
            },
            "level_desc": {
                "资源充足": "峰值利用率<0.85",
                "临近阈值": "0.85≤峰值利用率<0.93",
                "有压迫": "峰值利用率≥0.93",
                "未知(无Limit配置)": "无limit配置，无法判断"
            },
            "window_rule": "滑动窗口长度=采样点数/2，最小3个点，窗口区间=采样间隔×窗口长度"
        },
        "cpu_skew": {
            "score_rule": {
                "负载均衡": 100,
                "中度倾斜": 60,
                "严重倾斜": 0
            },
            "thresholds": {
                "balanced": 1.2,
                "critical": 4.0
            },
            "level_desc": {
                "负载均衡": "倾斜率≤1.2",
                "中度倾斜": "1.2<倾斜率≤4.0",
                "严重倾斜": "倾斜率>4.0"
            }
        },
        "cpu_request_utilization_p95": {
            "score_rule": {
                "可降低Request": 100,
                "配置合理": 80,
                "略超Request": 60,
                "严重超Request": 0,
                "未配置Request": 0
            },
            "thresholds": {
                "optimal": 0.8,
                "risk": 1.0
            },
            "level_desc": {
                "可降低Request": "P95利用率<0.8",
                "配置合理": "0.8≤P95利用率≤1.0",
                "略超Request": "1.0<P95利用率≤1.2",
                "严重超Request": "P95利用率>1.2",
                "未配置Request": "无request配置，无法判断"
            }
        },
        "cpu_redundancy": {
            "score_rule": {
                "冗余充足": 100,
                "冗余适中": 80,
                "冗余不足": 40,
                "严重不足": 0,
                "未配置Limit": 0
            },
            "buffer_rule": {
                "latency-critical": 0.3,
                "batch-job": 0.1,
                "default": 0.2
            },
            "level_desc": {
                "冗余充足": "冗余度≥20%（或服务类型对应安全缓冲）",
                "冗余适中": "10%≤冗余度<20%",
                "冗余不足": "0<冗余度<10%",
                "严重不足": "冗余度≤0",
                "未配置Limit": "无limit配置，无法判断"
            }
        }
    }
    default_data = [
        dict(type="CPU", key="cpu_avg", name="CPU平均值",
             description="计算高峰期内 CPU 使用的平均值，判断服务整体负载强度",
             rule="健康阈值：< 60% 的 CPU Limit 非常健康；60% 的 CPU Limit  < and < 80% 的 CPU Limit  健康；> 80% 的 CPU Limit  不健康"),
        dict(type="CPU", key="cpu_p95", name="CPU P95",
             description="计算高峰期内 CPU 使用的第 95 分位数，判断服务在大多数情况下的资源占用情况",
             rule="健康阈值：< 80% 的 CPU Limit 较健康，> 90% 有扩容风险"),
        dict(type="CPU", key="cpu_peak", name="CPU峰值",
             description="计算高峰期内的最大 CPU 使用值，判断极端时刻服务是否达到资源上限",
             rule="健康阈值：< 90% 的 CPU Limit 安全，> 95% 需警惕"),
        dict(type="CPU", key="cpu_std", name="CPU波动率",
             description="计算CPU 使用的标准差(波动率 / 离散度),判断资源使用的稳定性",
             rule="健康阈值：小于 0.1（单位为核数） → 非常稳定;大于 0.5 → 波动剧烈，可能有负载激增场景"),
        dict(type="CPU", key="cpu_slope", name="CPU趋势斜率",
             description="计算CPU 使用随时间变化的趋势，判断资源是否持续增长，有资源不足趋势",
             rule="健康阈值：>0 表示增长 → 持续压力增加 ;< 0 表示释放 → 压力下降"),
        dict(type="CPU", key="cpu_utilization_p95", name="CPU P95利用率",
             description="判断服务CPU的 P95 是否接近资源瓶颈( CPU使用P95 / CPU Limit)",
             rule="健康阈值：< 70%：资源充足；70% ~ 90%：可持续观测；90%：风险较高"),
        dict(type="CPU", key="cpu_utilization_peak", name="CPU峰值利用率",
             description="判断是否曾出现资源超载(CPU 峰值使用 / Limit)", rule="健康阈值：< 90%：合理；95%：有压迫，需关注"),
        dict(type="CPU", key="cpu_skew", name="CPU倾斜率",
             description="计算不同 Pod 之间 CPU 使用最大值与最小值之比(倾斜率),用于判断负载是否均衡",
             rule="健康阈值：≈ 1：负载均衡；≈ 2：倾斜严重；≈ 5：异常热点"),
        dict(type="CPU", key="cpu_request_utilization_p95", name="CPU P95/Request比值",
             description="计算P95 使用量相对于 CPU Request 的比例，评估 request 配置是否合理",
             rule="健康阈值：< 1：可调高 request，提高稳定性；> 1：可能触发 OOM、抢占，需调大 request"),
        dict(type="CPU", key="cpu_redundancy", name="CPU冗余率",
             description="计算长期未使用的资源，给出回收建议，提升资源利用率",
             rule="健康阈值：冗余 > 30%：可调低 limit，节约成本；冗余 < 10%：配置合理"),
    ]
    for item in default_data:
        rule_json = metric_rule_json_map.get(item["key"])
        exists = db.query(MetricCalcORM).filter_by(key=item["key"]).first()
        if not exists:
            db.add(MetricCalcORM(**item, rule_json=json.dumps(rule_json, ensure_ascii=False) if rule_json else None))
        else:
            # 如果已存在但 rule_json 为空，也补充
            if not exists.rule_json and rule_json:
                exists.rule_json = json.dumps(rule_json, ensure_ascii=False)
    db.commit()


def init_core_interface(db):
    """
    初始化核心接口表基础数据
    批量查询现有数据，对比后插入不存在的接口
    """

    default_data = [
        {"name": "获取课程信息", "url": "/api/classroom/*/info", "product_type": "classroom"},
        {"name": "根据用户及直播间ID更新优惠券缓存", "url": "/api/accelerate/live/object/coupon/update", "product_type": "coupon"},
        {"name": "获取人气值", "url": "/api/classroom/*/extra_lecture_info_api", "product_type": "classroom"},
        {"name": "消息列表", "url": "/api/classroom/*/message_and_discuss/list", "product_type": "classroom"},
        {"name": "获取红包列表", "url": "/api/classroom/*/redpack/delay_list", "product_type": "classroom"},
        {"name": "机器人用户列表", "url": "/api/get_robot_accounts", "product_type": "system"},
        {"name": "获取购买气氛", "url": "/api/classroom/*/purchase_tension/get", "product_type": "classroom"},
        {"name": "学员发送消息", "url": "/api/classroom/*/discuss/send", "product_type": "classroom"},
        {"name": "讲师撤回消息", "url": "/api/classroom/*/message/*/delete", "product_type": "classroom"},
        {"name": "获取商品信息", "url": "/api/accelerate/live/object/coupon/get", "product_type": "coupon"},
        {"name": "点击领取优惠券", "url": "/api/coupon/accept", "product_type": "coupon"},
        {"name": "推送商品", "url": "/api/classroom/*/card/send", "product_type": "classroom"},
    ]

    # 批量查询现有的所有接口URL
    existing_urls = set()
    existing_interfaces = db.query(CoreInterfaceORM.url).all()
    for interface in existing_interfaces:
        existing_urls.add(interface.url)

    # 对比数据，找出需要插入的新接口
    new_interfaces = []
    for item in default_data:
        if item["url"] not in existing_urls:
            new_interfaces.append(CoreInterfaceORM(
                interface_name=item["name"],
                url=item["url"],
                product_type=item["product_type"],
                status=1  # 默认启用
            ))

    # 批量插入新接口
    if new_interfaces:
        db.add_all(new_interfaces)
        db.commit()
        print(f"成功插入 {len(new_interfaces)} 个新的核心接口")
    else:
        print("所有核心接口已存在，无需插入新数据")
