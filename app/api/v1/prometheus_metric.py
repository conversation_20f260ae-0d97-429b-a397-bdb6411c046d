from fastapi import APIRouter, Depends, Query, Body, BackgroundTasks, HTTPException
from sqlalchemy.orm import Session
from app.deps.db import get_db
<<<<<<< HEAD
from app.services.source_collect.prometheus_collect import collect_grafana_metric, full_collect_grafana_metric, full_metric_calc_for_cluster
from app.services.metric_calc.prometheus_cpu_calc import get_app_cpu_avg, get_app_cpu_p95, get_app_cpu_peak, get_app_cpu_std, get_app_cpu_utilization_p95, get_app_cpu_utilization_peak, get_app_cpu_skew, get_app_cpu_request_utilization_p95, get_app_cpu_redundancy, get_app_cpu_slope
=======
from app.services.prometheus_monitor_service import collect_grafana_metric, get_app_cpu_avg, get_app_cpu_p95, get_app_cpu_peak, get_app_cpu_std, get_app_cpu_utilization_p95, get_app_cpu_utilization_peak, get_app_cpu_skew, get_app_cpu_request_utilization_p95, get_app_cpu_redundancy, full_collect_grafana_metric, get_app_cpu_slope, full_metric_calc_for_cluster
>>>>>>> sit
from typing import List, Dict, Any
from datetime import datetime, timedelta
from app.models.task_job import TaskJobORM
from app.core.config import TASK_STATUS_WAITING, TASK_STATUS_RUNNING
from app.models.metric_calc_result import MetricCalcResultORM
from app.models.metric_calc import MetricCalcORM
from app.models.metric_calc_result_detail import MetricCalcResultDetailORM
from sqlalchemy import desc
import json
from app.models.monitor import PromClusterServiceORM

router = APIRouter()

@router.post("/grafana_metric/collect", summary="采集Grafana指标数据")
def collect_grafana_metric_api(
    metric_id: int = Body(..., description="指标ID"),
    cluster: str = Body(..., description="集群名"),
    services: List[str] = Body(..., description="服务名列表"),
    special_suffixes: List[str] = Body(..., description="特殊后缀列表"),
    namespaces: Dict[str, str] = Body(..., description="服务-命名空间映射"),
    interval: str = Body("5m", description="采样区间"),
    start_time: str = Body(..., description="开始时间，格式如2025-06-11 00:00:00"),
    end_time: str = Body(..., description="结束时间，格式如2025-06-12 00:00:00"),
    db: Session = Depends(get_db)
):
    """
    采集Grafana指标数据,返回采集成功提示。
    """
    result = collect_grafana_metric(db, metric_id, cluster, services, special_suffixes, namespaces, interval, start_time, end_time)
    return result

@router.get("/grafana_metric/app/cpu_avg", summary="查询应用在高峰期的CPU平均值")
def get_app_cpu_avg_api(
    date: str = Query(..., description="查询日期，格式 YYYY-MM-DD", example="2025-06-20"),
    app: str = Query(..., description="应用名", example="main-weike"),
    service: str = Query(..., description="服务名", example="main-weike"),
    db: Session = Depends(get_db)
):
    return get_app_cpu_avg(db, date, app, service)

@router.get("/grafana_metric/app/cpu_p95", summary="查询应用在高峰期的CPU P95值")
def get_app_cpu_p95_api(
    date: str = Query(..., description="查询日期，格式 YYYY-MM-DD", example="2025-06-20"),
    app: str = Query(..., description="应用名", example="main-weike"),
    service: str = Query(..., description="服务名", example="main-weike"),
    db: Session = Depends(get_db)
):
    return get_app_cpu_p95(db, date, app, service)

@router.get("/grafana_metric/app/cpu_peak", summary="查询应用在高峰期的CPU峰值")
def get_app_cpu_peak_api(
    date: str = Query(..., description="查询日期，格式 YYYY-MM-DD", example="2025-06-20"),
    app: str = Query(..., description="应用名", example="main-weike"),
    service: str = Query(..., description="服务名", example="main-weike"),
    db: Session = Depends(get_db)
):
    return get_app_cpu_peak(db, date, app, service)

@router.get("/grafana_metric/app/cpu_slope", summary="查询应用在高峰期的CPU趋势斜率")
def get_app_cpu_slope_api(
    date: str = Query(..., description="查询日期，格式 YYYY-MM-DD", example="2025-06-20"),
    app: str = Query(..., description="应用名", example="main-weike"),
    service: str = Query(..., description="服务名", example="main-weike"),
    db: Session = Depends(get_db)
):
    return get_app_cpu_slope(db, date, app, service)

@router.get("/grafana_metric/app/cpu_std", summary="查询应用在高峰期的CPU波动率")
def get_app_cpu_std_api(
    date: str = Query(..., description="查询日期，格式 YYYY-MM-DD", example="2025-06-20"),
    app: str = Query(..., description="应用名", example="main-weike"),
    service: str = Query(..., description="服务名", example="main-weike"),
    db: Session = Depends(get_db)
):
    return get_app_cpu_std(db, date, app, service)

@router.get("/grafana_metric/app/cpu_utilization_p95", summary="查询应用在高峰期的P95 CPU利用率")
def get_app_cpu_utilization_p95_api(
    date: str = Query(..., description="查询日期，格式 YYYY-MM-DD", example="2025-06-20"),
    app: str = Query(..., description="应用名", example="main-weike"),
    service: str = Query(..., description="服务名", example="main-weike"),
    db: Session = Depends(get_db)
):
    return get_app_cpu_utilization_p95(db, date, app, service)

@router.get("/grafana_metric/app/cpu_utilization_peak", summary="查询应用在高峰期的CPU峰值利用率")
def get_app_cpu_utilization_peak_api(
    date: str = Query(..., description="查询日期，格式 YYYY-MM-DD", example="2025-06-20"),
    app: str = Query(..., description="应用名", example="main-weike"),
    service: str = Query(..., description="服务名", example="main-weike"),
    db: Session = Depends(get_db)
):
    return get_app_cpu_utilization_peak(db, date, app, service)

@router.get("/grafana_metric/app/cpu_skew", summary="查询应用在高峰期的CPU倾斜率")
def get_app_cpu_skew_api(
    date: str = Query(..., description="查询日期，格式 YYYY-MM-DD", example="2025-06-20"),
    app: str = Query(..., description="应用名", example="main-weike"),
    service: str = Query(..., description="服务名", example="main-weike"),
    db: Session = Depends(get_db)
):
    return get_app_cpu_skew(db, date, app, service)

@router.get("/grafana_metric/app/cpu_request_utilization_p95", summary="查询应用在高峰期的P95 CPU使用量与Request比值")
def get_app_cpu_request_utilization_p95_api(
    date: str = Query(..., description="查询日期，格式 YYYY-MM-DD", example="2025-06-20"),
    app: str = Query(..., description="应用名", example="main-weike"),
    service: str = Query(..., description="服务名", example="main-weike"),
    db: Session = Depends(get_db)
):
    return get_app_cpu_request_utilization_p95(db, date, app, service)

@router.get("/grafana_metric/app/cpu_redundancy", summary="查询应用在高峰期的冗余资源")
def get_app_cpu_redundancy_api(
    date: str = Query(..., description="查询日期，格式 YYYY-MM-DD", example="2025-06-20"),
    app: str = Query(..., description="应用名", example="main-weike"),
    service: str = Query(..., description="服务名", example="main-weike"),
    db: Session = Depends(get_db)
):
    return get_app_cpu_redundancy(db, date, app, service)

@router.post("/grafana_metric/full_collect", summary="全量异步采集Grafana指标数据")
def full_collect_grafana_metric_api(
    background_tasks: BackgroundTasks,
    metric_id: int = Body(..., description="指标ID"),
    collect_date: str = Body(..., description="采集日期，格式YYYY-MM-DD"),
    db: Session = Depends(get_db)
):
    """
    全量采集接口：
    1. 检查task_job表是否有同一天同指标的等待中/进行中任务，有则直接返回任务列表。
    2. 没有则组装采集参数，写入task_job，异步多线程启动采集。
    """
    # 查询是否有等待中/进行中的任务
    tasks = db.query(TaskJobORM).filter(
        TaskJobORM.metric_id == metric_id,
        TaskJobORM.collect_date == collect_date,
        TaskJobORM.status.in_([TASK_STATUS_WAITING, TASK_STATUS_RUNNING])
    ).all()
    if tasks:
        # 有任务在进行，直接返回
        return {
            "msg": "当前时间存在采集任务，需等待其采集完成",
            "tasks": [
                {
                    "id": t.id,
                    "status": t.status,
                    "collect_start_time": t.collect_start_time,
                    "collect_end_time": t.collect_end_time
                } for t in tasks
            ]
        }
    # 无任务，发起全量采集
    background_tasks.add_task(full_collect_grafana_metric, db, metric_id, collect_date)
    return {"msg": "采集任务已提交，正在异步执行"}

@router.post("/metric/full_calc_for_cluster", summary="全量执行指标计算（按集群/服务/应用）")
def full_metric_calc_for_cluster_api(
    date: str = Body(None, description="日期，格式yyyy-mm-dd，可选，默认昨天"),
    cluster_id: int = Body(..., description="集群ID"),
    service: str = Body(None, description="服务名，可选"),
    app: str = Body(None, description="应用名，可选"),
    source: str = Body('prometheus', description="来源，可选，默认prometheus"),
    task_type: str = Body('全量', description="任务类型，可选，默认全量"),
    restart: bool = Body(False, description="是否强制重算，默认false"),
    db: Session = Depends(get_db)
):
    """
    输入：日期、集群id，service/app可选。遍历集群下所有服务和应用，批量执行所有指标计算。
    支持异步、任务唯一性、失败重算、restart等业务逻辑。
    """
    return full_metric_calc_for_cluster(db, date, cluster_id, service, app, source, task_type, restart)

@router.get("/grafana_metric/app/cpu_score", summary="查询应用CPU相关指标得分")
def get_app_cpu_score_api(
    app: str = Query(..., description="应用名", example="main-weike"),
    date: str = Query(None, description="查询日期，格式 YYYY-MM-DD，可选，默认昨天"),
    db: Session = Depends(get_db)
):
    """
    查询指定app在某天的所有CPU相关指标得分及指标说明。
    返回：每个指标的metric_key、pod_count、健康得分、problem_pods_json（不为空时返回）、description、rule、key。
    """
    # 1. 日期处理
    if not date:
        date = (datetime.now() - timedelta(days=1)).strftime('%Y-%m-%d')
    # 2. 查询metric_calc_result表
    result_rows = db.query(MetricCalcResultORM).filter(
        MetricCalcResultORM.app == app,
        MetricCalcResultORM.collect_date == date
    ).all()
    result_list = []
    for row in result_rows:
        # 3. 查询metric_calc表
        metric_info = db.query(MetricCalcORM).filter(MetricCalcORM.key == row.metric_key).first()
        item = {
            "metric_key": row.metric_key,
            "pod_count": row.pod_count,
            "health_score": row.health_score
        }
        if row.problem_pods_json:
            item["problem_pods_json"] = row.problem_pods_json
        if metric_info:
            item["description"] = metric_info.description
            item["rule"] = metric_info.rule
            item["key"] = metric_info.key
        result_list.append(item)
    return {"app": app, "date": date, "metrics": result_list}

@router.get("/grafana_metric/app/metric_detail", summary="查询应用指定指标的明细数据（自动分发）")
def get_app_metric_detail_api(
    app: str = Query(..., description="应用名"),
    metric: str = Query(..., description="指标key，如cpu_std等"),
    date: str = Query(None, description="查询日期，格式 YYYY-MM-DD，可选，不传则取最新"),
    service: str = Query(..., description="服务名"),
    db: Session = Depends(get_db)
):
    """
    优化：自动根据 metric 枚举分发到对应指标函数，参数为 app, metric, date, service。
    """
    metric_func_map = {
        "cpu_avg": get_app_cpu_avg,
        "cpu_p95": get_app_cpu_p95,
        "cpu_peak": get_app_cpu_peak,
        "cpu_std": get_app_cpu_std,
        "cpu_utilization_p95": get_app_cpu_utilization_p95,
        "cpu_utilization_peak": get_app_cpu_utilization_peak,
        "cpu_skew": get_app_cpu_skew,
        "cpu_request_utilization_p95": get_app_cpu_request_utilization_p95,
        "cpu_redundancy": get_app_cpu_redundancy,
        "cpu_slope": get_app_cpu_slope,
    }
    if metric not in metric_func_map:
        raise HTTPException(status_code=400, detail=f"不支持的指标: {metric}")
    # 处理日期
    if not date:
        latest_row = db.query(MetricCalcResultDetailORM).filter(
            MetricCalcResultDetailORM.app == app,
            MetricCalcResultDetailORM.metric_key == metric,
            MetricCalcResultDetailORM.service == service
        ).order_by(desc(MetricCalcResultDetailORM.collect_date)).first()
        if not latest_row:
            return {"msg": "无数据"}
        date = latest_row.collect_date.strftime("%Y-%m-%d")
    # 自动分发到对应指标函数
    return metric_func_map[metric](db, date, app, service)
