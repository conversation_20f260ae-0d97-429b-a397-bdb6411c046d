from fastapi import APIRouter, Depends, Query
from sqlalchemy.orm import Session
from app.deps.db import get_db
from app.services.source_collect.cls_online_users_collect import fetch_peak_online_users, save_peak_online_users
from app.services.source_collect.cls_slow_api_collect import fetch_slow_api_details
from app.services.metric_calc.cls_slow_api_and_online_calc import aggregate_slow_api_by_day, generate_slow_api_report, get_slow_api_report_detail, get_last_natural_week
from datetime import datetime, timedelta
from tencentcloud.common.exception.tencent_cloud_sdk_exception import TencentCloudSDKException
from fastapi import HTTPException
from app.models.peak_online_user import PeakOnlineUserORM
from app.models.slow_api_agg import SlowApiAggORM
from sqlalchemy import func

router = APIRouter()

@router.post("/peak_online_user/collect", summary="手动采集高峰期每分钟在线人数")
def collect_peak_online_user(
    query: str = Query(..., description="CLS SQL查询语句"),
    start_time: str = Query(None, description="查询起始时间，格式如 '2025-06-11 19:00:00'"),
    end_time: str = Query(None, description="查询结束时间，格式如 '2025-06-11 19:10:00'"),
    db: Session = Depends(get_db)
):
    """
    手动触发采集腾讯云CLS在线人数数据，并写入数据库
    实现逻辑：
    1. 解析时间参数，转为时间戳
    2. 调用fetch_peak_online_users采集数据
    3. 调用save_peak_online_users批量写入数据库
    4. 捕获CLS超时等异常，返回友好提示
    """
    def parse_time(timestr, is_end=False):
        """
        兼容只传递年月日格式，自动补全为 00:00:00 或 23:59:59（如果需要）
        """
        if not timestr:
            return None
        try:
            # 只传日期
            if len(timestr) == 10:
                if is_end:
                    # 补全为当天23:59:59
                    timestr = timestr + ' 23:59:59'
                else:
                    timestr = timestr + ' 00:00:00'
            return int(datetime.strptime(timestr, '%Y-%m-%d %H:%M:%S').timestamp())
        except Exception:
            return int(datetime.strptime(timestr, '%Y-%m-%d %H:%M:%S.%f').timestamp())
    now = datetime.now()
    st = parse_time(start_time, False) if start_time else int((now - timedelta(hours=1)).timestamp())
    et = parse_time(end_time, True) if end_time else int(now.timestamp())
    try:
        data = fetch_peak_online_users(query, st, et)
        save_peak_online_users(db, data)
    except TencentCloudSDKException as e:
        if 'SearchTimeout' in str(e):
            return {"msg": "CLS查询超时，请稍后重试", "detail": str(e)}
        raise HTTPException(status_code=500, detail=f"CLS查询异常: {e}")
    return {"msg": "采集成功", "count": len(data), "data": data}

@router.post("/slow_api_detail/collect", summary="手动采集慢接口明细")
def collect_slow_api_detail(
    query: str = Query(..., description="CLS SQL查询语句"),
    start_time: str = Query(None, description="查询起始时间，格式如 '2025-06-11 00:00:00'"),
    end_time: str = Query(None, description="查询结束时间，格式如 '2025-06-12 00:00:00'"),
    db: Session = Depends(get_db)
):
    """
    手动触发采集腾讯云CLS慢接口明细数据，并写入数据库
    实现逻辑：
    1. 解析时间参数，转为时间戳
    2. 调用fetch_slow_api_details采集数据并写入数据库
    3. 捕获CLS超时等异常，返回友好提示
    """
    def parse_time(timestr, is_end=False):
        """
        兼容只传递年月日格式，自动补全为 00:00:00 或 23:59:59（如果需要）
        """
        if not timestr:
            return None
        try:
            # 只传日期
            if len(timestr) == 10:
                if is_end:
                    # 补全为当天23:59:59
                    timestr = timestr + ' 23:59:59'
                else:
                    timestr = timestr + ' 00:00:00'
            return int(datetime.strptime(timestr, '%Y-%m-%d %H:%M:%S').timestamp())
        except Exception:
            return int(datetime.strptime(timestr, '%Y-%m-%d %H:%M:%S.%f').timestamp())
    now = datetime.now()
    st = parse_time(start_time, False) if start_time else int((now - timedelta(hours=1)).timestamp())
    et = parse_time(end_time, True) if end_time else int(now.timestamp())
    try:
        result = fetch_slow_api_details(query, st, et, db)
    except TencentCloudSDKException as e:
        if 'SearchTimeout' in str(e):
            return {"msg": "CLS查询超时，请稍后重试", "detail": str(e)}
        raise HTTPException(status_code=500, detail=f"CLS查询异常: {e}")
    return {"msg": "采集成功", **result}

@router.post("/slow_api_agg/aggregate", summary="慢API明细按天聚合")
def aggregate_slow_api(
    target_date: str = Query(..., description="目标日期，格式如2025-06-11"),
    db: Session = Depends(get_db)
):
    """
    按天聚合慢API明细，写入slow_api_agg表
    实现逻辑：
    1. 查询slow_api_detail表中目标日期的所有明细
    2. 归一化path，按分钟和指纹聚合，统计QPS、均值、P95
    3. 只保留QPS大于100的数据
    4. 聚合后写入slow_api_agg表，已存在则更新
    """
    result = aggregate_slow_api_by_day(target_date, db)
    return result

@router.post("/slow_api_report/generate", summary="生成慢API性能报告")
def generate_slow_api_report_api(
    date: str = Query(None, description="指定日期，格式如2025-06-11，若不传则生成最近自然周报告"),
    db: Session = Depends(get_db)
):
    """
    生成指定日期所在自然周的慢API性能报告，自动写入 slow_api_report 和 slow_api_report_detail。
    若未传date则生成最近自然周。
    若该自然周数据不完整则直接返回提示。
    """
    try:
        # 解析date参数，获取目标自然周
        if date:
            try:
                target_date = datetime.strptime(date, "%Y-%m-%d").date()
            except Exception:
                return {"msg": "日期格式错误，应为YYYY-MM-DD"}
            start_date, end_date = get_last_natural_week(target_date)
        else:
            start_date, end_date = get_last_natural_week()
        # 检查该自然周数据是否完整（高峰在线人数+慢API聚合）
        peak_count = db.query(func.count()).select_from(PeakOnlineUserORM).filter(
            PeakOnlineUserORM.peak_time >= start_date,
            PeakOnlineUserORM.peak_time <= end_date
        ).scalar()
        agg_count = db.query(func.count()).select_from(SlowApiAggORM).filter(
            SlowApiAggORM.target_date >= start_date,
            SlowApiAggORM.target_date <= end_date
        ).scalar()
        if peak_count == 0:
            return {"msg": f"{start_date}~{end_date} 无高峰在线人数数据，无法生成报告"}
        if agg_count == 0:
            return {"msg": f"{start_date}~{end_date} 无慢API聚合数据，无法生成报告"}
        # 生成报告
        result = generate_slow_api_report(db, start_date, end_date)
        return result
    except Exception as e:
        return {"msg": f"生成失败: {str(e)}"}

@router.get("/slow_api_report/detail", summary="查看慢API性能报告")
def get_slow_api_report_detail_api(report_id: int = Query(..., description="报告ID"), db: Session = Depends(get_db)):
    """
    查看慢API性能报告，返回定制化文本和Top10数据。
    """
    return get_slow_api_report_detail(report_id, db)

@router.get("/slow_api_agg/list", summary="查询某一天的慢API详情（分页，按慢请求占比倒序）")
def list_slow_api_agg(
    date: str = Query(..., description="目标日期，格式如2025-06-11"),
    page: int = Query(1, description="页码，从1开始"),
    page_size: int = Query(50, description="每页条数，默认50"),
    db: Session = Depends(get_db)
):
    """
    查询指定日期的慢API聚合详情，按slow_ratio倒序分页返回。
    返回：总条数、明细数据（分页）。
    """
    date_obj = datetime.strptime(date, "%Y-%m-%d").date()
    query = db.query(SlowApiAggORM).filter(SlowApiAggORM.target_date == date_obj)
    total = query.count()
    items = query.order_by(SlowApiAggORM.slow_ratio.desc()) \
        .offset((page-1)*page_size).limit(page_size).all()
    # 转dict
    data = []
    for item in items:
        row = {c.name: getattr(item, c.name) for c in item.__table__.columns if c.name not in ("created_at", "updated_at")}
        if "qps" in row and row["qps"] is not None:
            try:
                row["qps"] = round(row["qps"] / 60, 2)
            except Exception:
                row["qps"] = 0
        data.append(row)
    return {"total": total, "data": data}

@router.get("/peak_online_user/list", summary="查询某一天的直播高峰期用户数量详情（按用户数倒序）")
def list_peak_online_user(
    date: str = Query(..., description="目标日期，格式如2025-06-11"),
    db: Session = Depends(get_db)
):
    """
    查询指定日期的 peak_online_user 表数据，按 user_count 倒序返回。
    返回的每条数据不包含 created_at 和 updated_at 字段。
    """
    date_obj = datetime.strptime(date, "%Y-%m-%d").date()
    query = db.query(PeakOnlineUserORM).filter(PeakOnlineUserORM.peak_time >= date_obj, PeakOnlineUserORM.peak_time < date_obj + timedelta(days=1))
    items = query.order_by(PeakOnlineUserORM.user_count.desc()).all()
    data = [
        {c.name: getattr(item, c.name) for c in item.__table__.columns if c.name not in ("created_at", "updated_at")}
        for item in items
    ]
    return {"data": data}
