from apscheduler.schedulers.background import BackgroundScheduler
from apscheduler.triggers.cron import CronTrigger
import atexit
from datetime import datetime, timedelta
from app.deps.db import SessionLocal
from app.services.metric_calc.cls_slow_api_and_online_calc import aggregate_slow_api_by_day, generate_slow_api_report, get_slow_api_report_detail
from app.services.source_collect.prometheus_collect import full_collect_grafana_metric
from app.models.slow_api_report import SlowApiReportORM
import requests
from app.core.config import SCHEDULE_CONFIG, WECHAT_WEBHOOK

scheduler = BackgroundScheduler()

# 1. 每天00:30采集metric_id=1
def job_metric_1():
    from app.api.v1.cls_metric import execute_metric
    db = SessionLocal()
    try:
        yesterday = (datetime.now() - timedelta(days=1)).strftime('%Y-%m-%d')
        execute_metric(metric_id=1, start_time=yesterday, end_time=yesterday, db=db)
    finally:
        db.close()

# 2. 每天00:40采集metric_id=2
def job_metric_2():
    from app.api.v1.cls_metric import execute_metric
    db = SessionLocal()
    try:
        yesterday = (datetime.now() - timedelta(days=1)).strftime('%Y-%m-%d')
        execute_metric(metric_id=2, start_time=yesterday, end_time=yesterday, db=db)
    finally:
        db.close()

# 3. 每天01:00聚合慢API
def job_agg_slow_api():
    db = SessionLocal()
    try:
        yesterday = (datetime.now() - timedelta(days=1)).strftime('%Y-%m-%d')
        aggregate_slow_api_by_day(yesterday, db)
    finally:
        db.close()

# 4. 每周一08:00生成慢API报告
def job_generate_report():
    db = SessionLocal()
    try:
        generate_slow_api_report(db)
    finally:
        db.close()

# 5. 每周一09:10推送慢API报告到企业微信
def job_push_report():
    db = SessionLocal()
    try:
        # 获取最新报告ID
        report = db.query(SlowApiReportORM).order_by(SlowApiReportORM.id.desc()).first()
        if not report:
            return
        report_id = report.id
        result = get_slow_api_report_detail(report_id, db)
        text = result.get('text', '')
        payload = {"msgtype": "text", "text": {"content": text}}
        requests.post(WECHAT_WEBHOOK, json=payload, timeout=10)
    finally:
        db.close()

# 6. 每天00:10采集metric_id=3（prometheus采集）
def job_metric_3_prometheus():
    db = SessionLocal()
    try:
        yesterday = (datetime.now() - timedelta(days=1)).strftime('%Y-%m-%d')
        # 直接调用全量采集主逻辑（会自动调度所有子任务）
        full_collect_grafana_metric(db, metric_id=3, collect_date=yesterday)
    finally:
        db.close()

# 7. 每天01:00全量计算prometheus指标（cluster_id=1）
def job_full_metric_calc_cluster1():
    from app.services.prometheus_service import full_metric_calc_for_cluster
    db = SessionLocal()
    try:
        yesterday = (datetime.now() - timedelta(days=1)).strftime('%Y-%m-%d')
        full_metric_calc_for_cluster(
            db,
            date=yesterday,
            cluster_id=1,
            source="prometheus",
            task_type="全量",
            restart=False
        )
    finally:
        db.close()

def start_scheduler():
    # 每天00:30
    scheduler.add_job(job_metric_1, CronTrigger(**SCHEDULE_CONFIG['metric_1']), id="job_metric_1", replace_existing=True)
    # 每天00:40
    scheduler.add_job(job_metric_2, CronTrigger(**SCHEDULE_CONFIG['metric_2']), id="job_metric_2", replace_existing=True)
    # 每天01:00
    scheduler.add_job(job_agg_slow_api, CronTrigger(**SCHEDULE_CONFIG['agg_slow_api']), id="job_agg_slow_api", replace_existing=True)
    # 每周一08:00
    scheduler.add_job(job_generate_report, CronTrigger(**SCHEDULE_CONFIG['generate_report']), id="job_generate_report", replace_existing=True)
    # 每周一09:10
    scheduler.add_job(job_push_report, CronTrigger(**SCHEDULE_CONFIG['push_report']), id="job_push_report", replace_existing=True)
    # 每天00:10
    scheduler.add_job(job_metric_3_prometheus, CronTrigger(hour=0, minute=10), id="job_metric_3_prometheus", replace_existing=True)
    # 每天01:00
    scheduler.add_job(job_full_metric_calc_cluster1, CronTrigger(hour=1, minute=0), id="job_full_metric_calc_cluster1", replace_existing=True)
    scheduler.start()
    atexit.register(lambda: scheduler.shutdown()) 