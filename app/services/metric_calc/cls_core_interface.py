# 标准库
from datetime import datetime, timedelta
from fastapi import Depends
from app.deps.db import get_db
# 第三方库
from dotenv import load_dotenv
from sqlalchemy.orm import Session

# 本地模块
from app.core.log import logger
from app.services.utils import common_cls_query_cls_data
from app.models.core_interface import CoreInterfaceORM
from app.models.core_interface_performance_detail import CoreInterfacePerformanceDetailORM

load_dotenv()


def collect_core_interface_performance(target_date: str, db: Session, url=None) -> dict:
    """
    采集核心接口性能数据，写入core_interface_performance_detail表。

    业务背景：
    对核心接口进行性能监控，采集变异系数、QPS等指标，用于接口性能分析和优化。

    实现逻辑：
    1. 从core_interface表读取所有启用的接口URL
    2. 逐个接口查询CLS数据，计算性能指标
    3. 只保留变异系数>0.5且QPS>100的数据
    4. 写入core_interface_performance_detail表

    限制条件：
    - 一个path获取一次CLS数据，防止数据过于庞大
    - 只处理启用状态的接口

    典型用途：
    - 核心接口性能监控
    - 接口抖动检测
    - 性能瓶颈分析

    :param target_date: 目标日期（'YYYY-MM-DD'）
    :param db: SQLAlchemy数据库会话
    :parma url: 指定URL
    :return: {'inserted': int, 'processed': int, 'msg': str}
    """
    # 解析目标日期
    date_obj = datetime.strptime(target_date, '%Y-%m-%d').date()
    if url:
        interfaces = db.query(CoreInterfaceORM).filter(CoreInterfaceORM.url
                                                       == url,
                                                       CoreInterfaceORM.status == 1).first()
    else:
        # 查询所有启用的核心接口
        interfaces = db.query(CoreInterfaceORM).filter(
            CoreInterfaceORM.status == 1
        ).all()

    if not interfaces:
        return {"inserted": 0, "processed": 0, "msg": "无启用的核心接口"}

    logger.info(f"开始采集 {len(interfaces)} 个核心接口的性能数据，目标日期: {target_date}")

    # 计算时间范围（目标日期的全天）
    start_datetime = datetime.combine(date_obj, datetime.min.time())
    end_datetime = start_datetime + timedelta(days=1)
    start_time = int(start_datetime.timestamp())
    end_time = int(end_datetime.timestamp())

    inserted_count = 0
    processed_count = 0

    # 逐个接口处理
    for interface in interfaces:
        try:
            processed_count += 1
            path = interface.url

            logger.info(f"正在处理接口: {interface.interface_name} - {path}")

            # 构建CLS查询语句
            query = (
                f'path: "{path}" | '
                f'SELECT count(*) as total_qps, '
                f'histogram(cast(start_time AS timestamp), INTERVAL 1 MINUTE) AS minute_time, '
                f'CASE '
                f'  WHEN AVG(duration) > 0 THEN (MAX(duration) - MIN(duration)) / AVG(duration) '
                f'  ELSE 0 '
                f'END AS "简易抖动率", '
                f'STDDEV(duration) AS "响应抖动(ms)", '
                f'CASE '
                f'  WHEN AVG(duration) > 0 THEN STDDEV(duration) / AVG(duration) '
                f'  ELSE 0 '
                f'END AS "变异系数" '
                f'GROUP BY minute_time '
                f'HAVING "变异系数" > 0.5 and total_qps > 100 '
                f'ORDER BY minute_time '
                f'LIMIT 100000'
            )

            logger.debug(f"CLS查询语句: {query}")

            # 查询CLS数据
            cls_results = common_cls_query_cls_data(query, start_time, end_time)

            if not cls_results:
                logger.info(f"接口 {path} 无符合条件的性能数据")
                continue

            # 处理查询结果，写入数据库
            # for result in cls_results:
            #     try:
            #         # 解析结果数据
            #         total_qps = int(result.get('total_qps', 0))
            #         minute_time_str = result.get('minute_time', '')
            #         coefficient_of_variation = float(result.get('变异系数', 0))
            #
            #         # 解析时间
            #         if minute_time_str:
            #             record_time = datetime.strptime(minute_time_str, '%Y-%m-%d %H:%M:%S')
            #         else:
            #             continue
            #
            #         # 创建性能记录
            #         performance_record = CoreInterfacePerformanceDetailORM(
            #             url_path=path,
            #             url_name=interface.interface_name,
            #             coefficient_of_variation=Decimal(str(coefficient_of_variation)),
            #             total_qps=total_qps,
            #             record_time=record_time
            #         )
            #
            #         # 检查是否已存在相同记录（避免重复插入）
            #         existing = db.query(CoreInterfacePerformanceDetailORM).filter(
            #             CoreInterfacePerformanceDetailORM.url_path == path,
            #             CoreInterfacePerformanceDetailORM.record_time == record_time
            #         ).first()
            #
            #         if not existing:
            #             db.add(performance_record)
            #             inserted_count += 1
            #             logger.info(
            #                 f"插入性能记录: {interface.interface_name} - {record_time} - CV:{coefficient_of_variation}")
            #         else:
            #             logger.info(f"记录已存在，跳过: {interface.interface_name} - {record_time}")
            #
            #     except Exception as e:
            #         logger.error(f"处理性能数据时出错: {e}, 数据: {result}")
            #         continue
            #
            # # 提交当前接口的数据
            # db.commit()

        except Exception as e:
            logger.error(f"处理接口 {interface.interface_name} 时出错: {e}")
            db.rollback()
            continue

    logger.info(f"核心接口性能数据采集完成，处理 {processed_count} 个接口，插入 {inserted_count} 条记录")

    return {
        "inserted": inserted_count,
        "processed": processed_count,
        "msg": f"采集完成，处理 {processed_count} 个接口，插入 {inserted_count} 条性能记录"
    }





def get_core_interface_performance_summary(interface_id: int, start_date: str, end_date: str, db: Session) -> dict:
    """
    获取指定接口在时间范围内的性能汇总

    :param interface_id: 接口ID
    :param start_date: 开始日期
    :param end_date: 结束日期
    :param db: 数据库会话
    :return: 性能汇总数据
    """
    start_dt = datetime.strptime(start_date, '%Y-%m-%d')
    end_dt = datetime.strptime(end_date, '%Y-%m-%d') + timedelta(days=1)

    records = db.query(CoreInterfacePerformanceDetailORM).filter(
        CoreInterfacePerformanceDetailORM.url_id == interface_id,
        CoreInterfacePerformanceDetailORM.record_time >= start_dt,
        CoreInterfacePerformanceDetailORM.record_time < end_dt
    ).all()

    if not records:
        return {"msg": "无性能数据"}

    # 计算汇总指标
    total_records = len(records)
    avg_cv = sum(float(r.coefficient_of_variation) for r in records) / total_records
    max_cv = max(float(r.coefficient_of_variation) for r in records)
    total_qps = sum(r.total_qps for r in records)

    return {
        "interface_id": interface_id,
        "total_records": total_records,
        "avg_coefficient_of_variation": round(avg_cv, 4),
        "max_coefficient_of_variation": round(max_cv, 4),
        "total_qps": total_qps,
        "start_date": start_date,
        "end_date": end_date
    }





if __name__ == '__main__':
    collect_core_interface_performance(target_date='2025-07-16',db=db)