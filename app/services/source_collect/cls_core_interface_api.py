# 标准库
import json
from datetime import datetime, timedelta
from decimal import Decimal

# 第三方库
from dotenv import load_dotenv
from sqlalchemy import text, tuple_
from sqlalchemy.orm import Session

# 本地模块
from app.core.log import logger
from app.models.core_interface import CoreInterfaceORM
from app.models.core_interface_performance_detail import CoreInterfacePerformanceDetailORM
from app.integrations.tencent_cls.client import TencentCLSClient

load_dotenv()


def collect_core_interface_performance(target_date: str, db: Session, url: str = None) -> dict:
    """
    采集核心接口性能数据，写入core_interface_performance_detail表。

    业务背景：
    对核心接口进行性能监控，采集变异系数、QPS等指标，用于接口性能分析和优化。

    实现逻辑：
    1. 从core_interface表读取所有启用的接口URL
    2. 逐个接口查询CLS数据，使用Context分页
    3. 只保留变异系数>0.5且QPS>100的数据
    4. 批量写入core_interface_performance_detail表

    限制条件：
    - 一个path获取一次CLS数据，防止数据过于庞大
    - 只处理启用状态的接口
    - 使用Context分页机制

    典型用途：
    - 核心接口性能监控
    - 接口抖动检测
    - 性能瓶颈分析

    :param target_date: 目标日期（'YYYY-MM-DD'）
    :param db: SQLAlchemy数据库会话
    :param url: 可选，指定单个URL进行采集
    :return: {'inserted': int, 'processed': int, 'msg': str}
    """
    # 解析目标日期
    date_obj = datetime.strptime(target_date, '%Y-%m-%d').date()

    # 查询接口
    if url:
        interfaces = db.query(CoreInterfaceORM).filter(
            CoreInterfaceORM.url == url,
            CoreInterfaceORM.status == 1
        ).all()
    else:
        interfaces = db.query(CoreInterfaceORM).filter(
            CoreInterfaceORM.status == 1
        ).all()

    if not interfaces:
        return {"inserted": 0, "processed": 0, "msg": "无启用的核心接口"}

    logger.debug(f"开始采集 {len(interfaces)} 个核心接口的性能数据，目标日期: {target_date}")

    # 计算时间范围（目标日期的全天）
    start_datetime = datetime.combine(date_obj, datetime.min.time())
    end_datetime = start_datetime + timedelta(days=1)
    start_time = int(start_datetime.timestamp())
    end_time = int(end_datetime.timestamp())

    total_inserted = 0
    processed_count = 0

    # 逐个接口处理
    for interface in interfaces:
        try:
            processed_count += 1
            path = interface.url

            logger.info(f"正在处理接口: {interface.interface_name} - {path}")

            # 调用单个接口的性能数据采集
            result = fetch_interface_performance_data(
                interface, path, start_time, end_time, db
            )
            total_inserted += result.get("inserted", 0)

        except Exception as e:
            logger.error(f"处理接口 {interface.interface_name} 时出错: {e}")
            continue

    logger.info(f"核心接口性能数据采集完成，处理 {processed_count} 个接口，插入 {total_inserted} 条记录")

    return {
        "inserted": total_inserted,
        "processed": processed_count,
        "msg": f"采集完成，处理 {processed_count} 个接口，插入 {total_inserted} 条性能记录"
    }


def fetch_interface_performance_data(interface: CoreInterfaceORM, path: str, start_time: int, end_time: int, db: Session) -> dict:
    """
    采集单个接口的性能数据，使用Context分页机制

    :param interface: 接口ORM对象
    :param path: 接口路径
    :param start_time: 开始时间戳
    :param end_time: 结束时间戳
    :param db: 数据库会话
    :return: {'inserted': int}
    """
    # 构建CLS查询语句
    query = (
        f'path: "{path}" | '
        f'SELECT count(*) as total_qps, '
        f'histogram(cast(start_time AS timestamp), INTERVAL 1 MINUTE) AS minute_time, '
        f'CASE '
        f'  WHEN AVG(duration) > 0 THEN (MAX(duration) - MIN(duration)) / AVG(duration) '
        f'  ELSE 0 '
        f'END AS "简易抖动率", '
        f'STDDEV(duration) AS "响应抖动(ms)", '
        f'CASE '
        f'  WHEN AVG(duration) > 0 THEN STDDEV(duration) / AVG(duration) '
        f'  ELSE 0 '
        f'END AS "变异系数" '
        f'GROUP BY minute_time '
        f'HAVING "变异系数" > 0.5 and total_qps > 100 '
        f'ORDER BY minute_time '
        f'LIMIT 100000'
    )

    logger.info(f"CLS查询语句: {query}")

    client = TencentCLSClient()
    context = ""
    last_context = None
    total_inserted = 0

    while True:
        resp = client.search_log(query, start_time, end_time, limit=100, context=context)
        resp_json = json.loads(resp.to_json_string())

        batch = []
        for item in resp_json.get("AnalysisResults", []):
            data = {d["Key"]: d["Value"] for d in item["Data"]}

            # 解析数据
            total_qps = int(data.get('total_qps', 0))
            minute_time_str = data.get('minute_time', '')
            coefficient_of_variation = float(data.get('变异系数', 0))

            # 解析时间
            if minute_time_str:
                try:
                    record_time = datetime.strptime(minute_time_str, '%Y-%m-%d %H:%M:%S')
                except ValueError:
                    continue
            else:
                continue

            batch.append({
                "url_id": interface.id,
                "url_name": interface.interface_name,
                "coefficient_of_variation": Decimal(str(coefficient_of_variation)),
                "total_qps": total_qps,
                "record_time": record_time
            })

        if not batch:
            break

        # 批量插入数据
        inserted_count = batch_insert_performance_data(batch, db)
        total_inserted += inserted_count

        # 检查分页结束条件
        new_context = resp_json.get("Context", "")
        if resp_json.get("ListOver", True):
            break
        if new_context == last_context:
            break
        context = new_context
        last_context = context

    return {"inserted": total_inserted}


def batch_insert_performance_data(batch: list, db: Session) -> int:
    """
    批量插入性能数据的公共函数

    :param batch: 待插入的数据列表
    :param db: 数据库会话
    :return: 实际插入的记录数
    """
    if not batch:
        return 0

    # 批量查重 - 使用 url_id 和 record_time 作为唯一键
    keys = set((item["url_id"], item["record_time"]) for item in batch)
    existing = db.query(
        CoreInterfacePerformanceDetailORM.url_id,
        CoreInterfacePerformanceDetailORM.record_time
    ).filter(
        tuple_(
            CoreInterfacePerformanceDetailORM.url_id,
            CoreInterfacePerformanceDetailORM.record_time
        ).in_(keys)
    ).all()
    existing_set = set(existing)

    # 只插入不存在的记录
    to_insert = [
        CoreInterfacePerformanceDetailORM(
            url_id=item["url_id"],
            url_name=item["url_name"],
            coefficient_of_variation=item["coefficient_of_variation"],
            total_qps=item["total_qps"],
            record_time=item["record_time"]
        )
        for item in batch
        if (item["url_id"], item["record_time"]) not in existing_set
    ]

    if not to_insert:
        logger.info("所有记录已存在，跳过插入")
        return 0

    # 批量 upsert 使用 MySQL 的 INSERT ... ON DUPLICATE KEY UPDATE
    from sqlalchemy.dialects.mysql import insert

    data_list = []
    for obj in to_insert:
        data_list.append({
            "url_id": obj.url_id,
            "url_name": obj.url_name,
            "coefficient_of_variation": obj.coefficient_of_variation,
            "total_qps": obj.total_qps,
            "record_time": obj.record_time,
        })

    if data_list:
        BATCH_SIZE = 500  # 每批 upsert 500 条
        inserted_count = 0

        for i in range(0, len(data_list), BATCH_SIZE):
            batch_data = data_list[i:i+BATCH_SIZE]
            stmt = insert(CoreInterfacePerformanceDetailORM).values(batch_data)
            update_dict = {
                "url_name": stmt.inserted.url_name,
                "coefficient_of_variation": stmt.inserted.coefficient_of_variation,
                "total_qps": stmt.inserted.total_qps,
                "update_time": text("NOW()"),
            }
            stmt = stmt.on_duplicate_key_update(**update_dict)
            db.execute(stmt)
            inserted_count += len(batch_data)

        db.commit()
        logger.info(f"批量插入 {inserted_count} 条性能记录")
        return len(to_insert)

    return 0


def get_core_interface_performance_summary(interface_id: int, start_date: str, end_date: str, db: Session) -> dict:
    """
    获取指定接口在时间范围内的性能汇总

    :param interface_id: 接口ID
    :param start_date: 开始日期
    :param end_date: 结束日期
    :param db: 数据库会话
    :return: 性能汇总数据
    """
    start_dt = datetime.strptime(start_date, '%Y-%m-%d')
    end_dt = datetime.strptime(end_date, '%Y-%m-%d') + timedelta(days=1)

    records = db.query(CoreInterfacePerformanceDetailORM).filter(
        CoreInterfacePerformanceDetailORM.url_id == interface_id,
        CoreInterfacePerformanceDetailORM.record_time >= start_dt,
        CoreInterfacePerformanceDetailORM.record_time < end_dt
    ).all()

    if not records:
        return {"msg": "无性能数据"}

    # 计算汇总指标
    total_records = len(records)
    avg_cv = sum(float(r.coefficient_of_variation) for r in records) / total_records
    max_cv = max(float(r.coefficient_of_variation) for r in records)
    total_qps = sum(r.total_qps for r in records)

    return {
        "interface_id": interface_id,
        "total_records": total_records,
        "avg_coefficient_of_variation": round(avg_cv, 4),
        "max_coefficient_of_variation": round(max_cv, 4),
        "total_qps": total_qps,
        "start_date": start_date,
        "end_date": end_date
    }





def get_core_interface_performance_summary(interface_id: int, start_date: str, end_date: str, db: Session) -> dict:
    """
    获取指定接口在时间范围内的性能汇总

    :param interface_id: 接口ID
    :param start_date: 开始日期
    :param end_date: 结束日期
    :param db: 数据库会话
    :return: 性能汇总数据
    """
    start_dt = datetime.strptime(start_date, '%Y-%m-%d')
    end_dt = datetime.strptime(end_date, '%Y-%m-%d') + timedelta(days=1)

    records = db.query(CoreInterfacePerformanceDetailORM).filter(
        CoreInterfacePerformanceDetailORM.url_id == interface_id,
        CoreInterfacePerformanceDetailORM.record_time >= start_dt,
        CoreInterfacePerformanceDetailORM.record_time < end_dt
    ).all()

    if not records:
        return {"msg": "无性能数据"}

    # 计算汇总指标
    total_records = len(records)
    avg_cv = sum(float(r.coefficient_of_variation) for r in records) / total_records
    max_cv = max(float(r.coefficient_of_variation) for r in records)
    total_qps = sum(r.total_qps for r in records)

    return {
        "interface_id": interface_id,
        "total_records": total_records,
        "avg_coefficient_of_variation": round(avg_cv, 4),
        "max_coefficient_of_variation": round(max_cv, 4),
        "total_qps": total_qps,
        "start_date": start_date,
        "end_date": end_date
    }





if __name__ == '__main__':
    collect_core_interface_performance(target_date='2025-07-16',db=db)