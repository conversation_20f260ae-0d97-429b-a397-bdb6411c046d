import os
import numpy as np
from typing import List, Dict, Any, Optional
from scipy import stats
from sklearn.linear_model import LinearRegression
from app.models.metric_calc import MetricCalcORM
import json
from app.core.log import logger


def get_peak_period(date: str) -> (str, str):
    """
    获取高峰期起止时间字符串
    """
    peak_start_time = os.getenv("PEAK_START_TIME", "18:50:00")
    peak_end_time = os.getenv("PEAK_END_TIME", "21:50:00")
    return f"{date} {peak_start_time}", f"{date} {peak_end_time}"

def parse_cpu_value(cpu_str: Optional[str]) -> float:
    """
    支持 m/C/c 单位的CPU字符串转float（核数），如'500m'→0.5，'4C'→4，'2'→2
    """
    if not cpu_str:
        return 0.0
    cpu_str = str(cpu_str).strip()
    if cpu_str.lower().endswith('m'):
        try:
            return float(cpu_str[:-1]) / 1000
        except Exception:
            return 0.0
    if cpu_str.lower().endswith('c'):
        try:
            return float(cpu_str[:-1])
        except Exception:
            return 0.0
    try:
        return float(cpu_str)
    except Exception:
        return 0.0

def query_and_group_pod_values(db, date, app, service, value_fields=None) -> Dict[str, List[float]]:
    """
    查询高峰期内指定服务/应用的所有pod的原始指标值，并按pod分组
    返回: {pod: [values]}
    """
    from app.models.grafana_metric_data import GrafanaCpuDataDetail
    peak_start, peak_end = get_peak_period(date)
    value_fields = value_fields or ['value']
    columns = [GrafanaCpuDataDetail.pod] + [getattr(GrafanaCpuDataDetail, f) for f in value_fields]
    rows = db.query(*columns).filter(
        GrafanaCpuDataDetail.service == service,
        GrafanaCpuDataDetail.app == app,
        GrafanaCpuDataDetail.timestamp >= peak_start,
        GrafanaCpuDataDetail.timestamp <= peak_end
    ).all()
    pod_values = {}
    for row in rows:
        pod = getattr(row, "pod", None) or row.get("pod")
        for field in value_fields:
            value = getattr(row, field, None) if hasattr(row, field) else row.get(field)
            if pod not in pod_values:
                pod_values[pod] = []
            pod_values[pod].append(value)
    return pod_values

# ===================== 统计与异常处理 =====================

def calc_stat(values: List[float], method: str = "avg", trim_ratio: float = 0.1) -> float:
    """
    通用统计函数：平均、最大、P95、鲁棒均值
    """
    if not values:
        return 0.0
    if method == "avg":
        return float(np.mean(values))
    elif method == "max":
        return float(np.max(values))
    elif method == "p95":
        return float(np.percentile(values, 95))
    elif method == "robust_avg":
        n = len(values)
        trim = max(1, int(trim_ratio * n))
        values_sorted = sorted(values)
        trimmed = values_sorted[trim:n-trim]
        return float(np.mean(trimmed)) if trimmed else float(np.median(values_sorted))
    else:
        raise ValueError("Unknown method")

def filter_outliers(values: List[float], method: str = "std", max_deviation: float = 3.0, trim_ratio: float = 0.1) -> List[float]:
    """
    异常值过滤：标准差法或截断法
    """
    if not values:
        return []
    if method == "std":
        mean, std = np.mean(values), np.std(values)
        return [v for v in values if abs(v - mean) < max_deviation * std] if std > 0 else values
    elif method == "trim":
        n = len(values)
        trim = max(1, int(trim_ratio * n))
        values_sorted = sorted(values)
        return values_sorted[trim:n-trim]
    else:
        return values

# ===================== 健康分级与判定 =====================

def classify_metric(value, thresholds: dict, mode: str, extra: dict = None) -> dict:
    """
    健康分级统一入口（已去除 color 相关逻辑，仅返回 level）
    mode: slope/std/peak/request/redundancy
    thresholds: 各模式下的分级阈值
    extra: 其它辅助参数
    """
    extra = extra or {}
    if mode == "slope":
        slope, r2, p = value, extra.get('r2', 0), extra.get('p', 1)
        is_significant = r2 >= thresholds.get('significance_r2', 0.7) and p < thresholds.get('significance_p', 0.05)
        if slope > thresholds.get('steep_slope', 0.5):
            return {'level': '紧急: 快速上升' if is_significant else '关注: 波动上升'}
        elif slope > thresholds.get('mild_slope', 0.0):
            return {'level': '预警: 缓慢上升' if is_significant else '正常: 噪声波动'}
        elif slope < 0:
            return {'level': '健康: 资源释放'}
        else:
            return {'level': '正常: 趋势平稳'}
    elif mode == "std":
        cpu_std, stable, volatile, pattern = value, thresholds['stable'], thresholds['volatile'], extra.get('pattern', {})
        if cpu_std < stable and pattern.get('spike_count', 0) == 0:
            return {'level': '非常稳定'}
        elif cpu_std >= volatile and pattern.get('spike_count', 0) > 3:
            return {'level': '异常波动'}
        elif cpu_std >= volatile:
            return {'level': '需关注'}
        elif pattern.get('type') == 'periodic':
            return {'level': '周期性波动'}
        else:
            return {'level': '一般波动'}
    elif mode == "peak":
        utilization = value
        if utilization is None:
            return {'level': '配置缺失'}
        if utilization < thresholds['safe']:
            return {'level': '安全'}
        elif utilization < thresholds['alert']:
            return {'level': '需关注'}
        else:
            return {'level': '高风险'}
    elif mode == "request":
        ratio = value
        if ratio is None:
            return {'level': '未配置Request'}
        if ratio > thresholds['risk']:
            return {'level': '严重超Request'}
        elif ratio > 1.0:
            return {'level': '略超Request'}
        elif ratio > thresholds['optimal']:
            return {'level': '配置合理'}
        else:
            return {'level': '可降低Request'}
    elif mode == "redundancy":
        redundancy_ratio, safe_buffer = value, thresholds['safe_buffer']
        if redundancy_ratio is None:
            return {'level': '未配置Limit'}
        if redundancy_ratio > safe_buffer * 1.5:
            return {'level': '冗余过多'}
        elif redundancy_ratio > safe_buffer * 0.8:
            return {'level': '配置合理'}
        elif redundancy_ratio > safe_buffer * 0.3:
            return {'level': '缓冲不足'}
        else:
            return {'level': '风险配置'}
    else:
        return {'level': '未知'}

# ===================== 业务分析与建议 =====================

def calculate_significant_slope(x, y):
    """
    计算斜率并返回统计显著性
    """
    slope, intercept, r_value, p_value, _ = stats.linregress(x, y)
    return {
        'slope': slope,
        'r_squared': r_value ** 2,
        'p_value': p_value,
        'is_significant': bool(p_value < 0.05)
    }

def find_steepest_slope_window(timestamps, values, window_size=2):
    """
    滑动窗口检测最陡峭上升时段，window_size=2表示10分钟（5分钟粒度）
    """
    max_slope, max_window = float('-inf'), []
    n = len(values)
    if n < window_size:
        return None, []
    for i in range(n - window_size + 1):
        window_x = np.array(timestamps[i:i+window_size]).reshape(-1, 1)
        window_y = np.array(values[i:i+window_size])
        reg = LinearRegression().fit(window_x, window_y)
        slope = reg.coef_[0]
        if slope > max_slope:
            max_slope = slope
            max_window = timestamps[i:i+window_size]
    return max_slope, max_window

def identify_fluctuation_pattern(values):
    """
    识别波动模式：周期性/突发性/稳态
    """
    autocorr = np.correlate(values - np.mean(values), values - np.mean(values), mode='full')
    autocorr = autocorr[len(autocorr)//2:]
    has_periodicity = any(autocorr[i] > 0.5 * autocorr[0] for i in range(1, min(5, len(autocorr))))
    z_scores = np.abs((values - np.mean(values)) / (np.std(values) + 1e-6))
    has_spikes = any(z > 3 for z in z_scores)
    return {
        'type': 'periodic' if has_periodicity else ('spiky' if has_spikes else 'steady'),
        'spike_count': sum(z > 3 for z in z_scores)
    }

def analyze_std_context(cpu_std, cpu_avg, cpu_peak, cpu_limit):
    """
    根据均值/峰值解释波动原因，低负载下绝对波动很小时不提示
    """
    if cpu_limit and cpu_std / cpu_limit < 0.02:
        return ""
    if cpu_std > 0.3 * cpu_avg:
        if cpu_peak > 0.9 * cpu_limit:
            return "高峰值导致高波动，请排查"
        elif cpu_avg < 0.3 * cpu_limit:
            return "低负载下的剧烈波动，请排查"
    return ""

def generate_suggestions(status: dict, pod_avgs: dict) -> List[str]:
    """
    生成针对性建议
    """
    suggestions = []
    if status['color'] == 'green':
        return ["当前负载均衡，无需立即操作"]
    top_pod = max(pod_avgs.items(), key=lambda x: x[1])
    suggestions.append(f"重点关注Pod: {top_pod[0]} (CPU平均 {top_pod[1]:.2f}核)")
    if status['color'] == 'yellow':
        suggestions.append("建议：检查该Pod的请求分布或资源配置")
    else:
        suggestions.append("紧急：立即检查以下情况：")
        suggestions.append("- 是否部署了新版本")
        suggestions.append("- 该Pod所在节点负载")
    if any('canary' in name.lower() for name in pod_avgs.keys()):
        suggestions.append("⚠️ 检测到灰度Pod，建议检查流量分配比例")
    return suggestions

def calc_sliding_window_peak(values, window_size):
    """
    计算滑动窗口内的最大均值（或最大值），用于平滑极端峰值。
    """
    if not values or window_size < 1:
        return 0
    n = len(values)
    if n <= window_size:
        return max(values)
    max_peak = max(np.mean(values[i:i+window_size]) for i in range(n - window_size + 1))
    return max_peak

def get_metric_rule(db, key):
    """
    根据指标key从metric_calc表获取rule_json配置，返回dict
    """
    obj = db.query(MetricCalcORM).filter_by(key=key).first()
    if obj and obj.rule_json:
        return json.loads(obj.rule_json)
    return {}

def parse_threshold(val, default):
    """
    将字符串阈值（如 '< 0.7'、'>=0.9'）转为 float，无法转换时返回默认值。
    """
    try:
        if isinstance(val, str):
            for s in ["<=", "<", ">=", ">", "~", " "]:
                val = val.replace(s, "")
            return float(val)
        return float(val)
    except Exception:
        return default

def common_cls_query_cls_data(query: str, start_time: int, end_time: int) -> list:

    """
    查询CLS性能数据，返回结果列表
    :param query: CLS SQL查询语句
    :param start_time: 查询起始时间（时间戳，秒）
    :param end_time: 查询结束时间（时间戳，秒）
    :return: 数据列表
    """
    try:
        from app.integrations.tencent_cls.client import TencentCLSClient

        client = TencentCLSClient()
        resp = client.search_log(query, start_time, end_time, limit=100000)
        resp_json = json.loads(resp.to_json_string())

        results = []
        for item in resp_json.get("AnalysisResults", []):
            data = {d["Key"]: d["Value"] for d in item["Data"]}
            results.append(data)

        logger.info(f"CLS查询返回 {len(results)} 条记录")
        return results

    except Exception as e:
        logger.error(f"CLS查询失败: {e}")
        return []


# 解析时间
def parse_time(timestr, is_end=False):
    """
    兼容只传递年月日格式，自动补全为 00:00:00 或 23:59:59（如果需要）
    """
    if not timestr:
        return None
    try:
        # 只传日期
        if len(timestr) == 10:
            if is_end:
                timestr = timestr + ' 23:59:59'
            else:
                timestr = timestr + ' 00:00:00'
        return int(datetime.strptime(timestr, '%Y-%m-%d %H:%M:%S').timestamp())
    except Exception:
        return int(datetime.strptime(timestr, '%Y-%m-%d %H:%M:%S.%f').timestamp())
