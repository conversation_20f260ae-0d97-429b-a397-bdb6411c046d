from pydantic import BaseModel, Field
from datetime import datetime
from typing import Optional
# SQLAlchemy ORM
from sqlalchemy import Column, Integer, DateTime, String, func, Index
from sqlalchemy.ext.declarative import declarative_base


class CoreInterfaceBase(BaseModel):
    """核心接口基础模型"""
    interface_name: str = Field(..., max_length=100, description="接口名称")
    url: str = Field(..., max_length=255, description="接口URL链接")
    product_type: str = Field(..., max_length=50, description="产品类型")
    status: int = Field(default=1, description="接口状态：0-禁用，1-启用")

class CoreInterfaceCreate(CoreInterfaceBase):
    """创建核心接口模型"""
    pass

class CoreInterfaceUpdate(BaseModel):
    """更新核心接口模型"""
    interface_name: Optional[str] = Field(None, max_length=100, description="接口名称")
    url: Optional[str] = Field(None, max_length=255, description="接口URL链接")
    product_type: Optional[str] = Field(None, max_length=50, description="产品类型")
    status: Optional[int] = Field(None, description="接口状态：0-禁用，1-启用")

class CoreInterface(CoreInterfaceBase):
    """核心接口完整模型"""
    id: int = Field(..., description="自增ID")
    create_time: datetime = Field(..., description="创建时间")
    update_time: datetime = Field(..., description="更新时间")

    class Config:
        from_attributes = True

Base = declarative_base()

class CoreInterfaceORM(Base):
    """
    核心接口表 ORM 模型
    字段均带有注释和字段描述
    """
    __tablename__ = "core_interface"

    id = Column(Integer, primary_key=True, autoincrement=True, comment="自增ID")
    interface_name = Column(String(100), nullable=False, comment="接口名称")
    url = Column(String(255), nullable=False, unique=True, comment="接口URL链接")
    product_type = Column(String(50), nullable=False, comment="产品类型")
    status = Column(Integer, nullable=False, default=1, comment="接口状态：0-禁用，1-启用")
    create_time = Column(DateTime, nullable=False, server_default=func.now(), comment="创建时间")
    update_time = Column(DateTime, nullable=False, server_default=func.now(), onupdate=func.now(), comment="更新时间")

    __table_args__ = (
        Index('idx_product_type', 'product_type'),
        Index('idx_status', 'status'),
        {'comment': '核心接口表'}
    )