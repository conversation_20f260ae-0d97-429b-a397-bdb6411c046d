from pydantic import BaseModel, Field
from datetime import datetime
from typing import Optional, Dict, Any
# SQLAlchemy ORM
from sqlalchemy import Column, BigInteger, DateTime, String, Integer, func, Index, JSON,DATE
from sqlalchemy.ext.declarative import declarative_base

Base = declarative_base()
class CoreApiJitterExceptionCreate(BaseModel):
    """API抖动异常创建模型 - 内部使用"""
    api_name: str = Field(..., max_length=100, description="API名称")
    duration: int = Field(..., description="抖动持续时间(毫秒)")
    trigger_details: Optional[Dict[str, Any]] = Field(None, description="触发异常明细记录")

class CoreApiJitterException(CoreApiJitterExceptionCreate):
    """API抖动异常完整模型 - 内部使用"""
    id: int = Field(..., description="自增ID")
    create_time: datetime = Field(..., description="记录创建时间")

    class Config:
        from_attributes = True



class CoreApiJitterExceptionORM(Base):
    """
    API抖动异常记录表 ORM 模型
    字段均带有注释和字段描述
    """
    __tablename__ = "core_api_jitter_exception"

    id = Column(BigInteger, primary_key=True, autoincrement=True, comment="自增ID")
    url_name = Column(String(100), nullable=False, comment="冗余核心接口名称")
    url_path = Column(String(100), nullable=False, comment="冗余核心接口path")
    duration = Column(Integer, nullable=False, comment="抖动持续时间(毫秒)")
    trigger_details = Column(JSON, nullable=True, comment="触发异常明细记录")
    create_time = Column(DateTime, nullable=False, server_default=func.now(), comment="记录创建时间")

    __table_args__ = (
        Index('idx_url_path', 'url_path'),
        Index('idx_create_time', 'create_time'),
        Index('idx_trigger_details','trigger_details'),
        {'comment': '核心接口抖动率异常表'}
    )

    @classmethod
    def create_record(cls, api_name: str, duration: int, trigger_details: Optional[Dict[str, Any]] = None):
        """
        创建API抖动异常记录的便捷方法
        用于内部函数调用
        """
        return cls(
            api_name=api_name,
            duration=duration,
            trigger_details=trigger_details
        )