from pydantic import BaseModel, Field
from datetime import datetime

class MetricBase(BaseModel):
    """指标基础模型"""
    metric_sql: str = Field(..., description="指标SQL")
    description: str = Field(..., description="指标描述")
    type_: str = Field(None, description="指标类型（可选）")

class MetricCreate(MetricBase):
    """创建指标模型"""
    pass

class Metric(MetricBase):
    """完整指标模型"""
    id: int = Field(..., description="主键ID")
    created_at: datetime = Field(..., description="创建时间")
    updated_at: datetime = Field(..., description="更新时间")

    class Config:
        from_attributes = True

# SQLAlchemy ORM
from sqlalchemy import Column, Integer, DateTime, String, Text, func
from sqlalchemy.ext.declarative import declarative_base

Base = declarative_base()

class MetricORM(Base):
    """
    指标表 ORM 模型
    """
    __tablename__ = "metric"
    id = Column(Integer, primary_key=True, autoincrement=True, comment="主键ID")
    metric_sql = Column(Text, nullable=False, comment="指标SQL")
    description = Column(String(255), nullable=False, comment="指标描述")
    type_ = Column(String(64), nullable=True, comment="指标类型（可选）")
    created_at = Column(DateTime, nullable=False, server_default=func.now(), comment="创建时间")
    updated_at = Column(DateTime, nullable=False, server_default=func.now(), onupdate=func.now(), comment="更新时间") 