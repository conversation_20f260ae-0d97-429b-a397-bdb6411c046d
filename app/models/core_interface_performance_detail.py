from pydantic import BaseModel, Field
from datetime import datetime
from decimal import Decimal

class CoreInterfacePerformanceDetailCreate(BaseModel):
    """接口性能指标创建模型 - 内部使用"""
    url_id: int = Field(..., description="接口ID(外键关联core_interface表)")
    url_name: str = Field(..., max_length=100, description="接口名称(冗余)")
    coefficient_of_variation: Decimal = Field(..., max_digits=10, decimal_places=4, description="变异系数(CV)")
    total_qps: int = Field(..., description="总QPS(每秒查询率)")
    record_time: datetime = Field(..., description="指标记录时间(从第三方采集的时间)")

class CoreInterfacePerformanceDetail(CoreInterfacePerformanceDetailCreate):
    """接口性能指标完整模型 - 内部使用"""
    id: int = Field(..., description="自增ID")
    create_time: datetime = Field(..., description="创建时间")
    update_time: datetime = Field(..., description="更新时间")

    class Config:
        from_attributes = True

# SQLAlchemy ORM
from sqlalchemy import Column, Integer, DateTime, String, func, Index, ForeignKey, DECIMAL
from sqlalchemy.ext.declarative import declarative_base

Base = declarative_base()

class CoreInterfacePerformanceDetailORM(Base):
    """
    接口性能指标表 ORM 模型
    字段均带有注释和字段描述
    """
    __tablename__ = "core_interface_performance_detail"

    id = Column(Integer, primary_key=True, autoincrement=True, comment="自增ID")
    url_name = Column(String(100), nullable=False, comment="冗余核心接口名称")
    url_path = Column(String(100), nullable=False, comment="冗余核心接口path")
    coefficient_of_variation = Column(DECIMAL(10, 4), nullable=False, comment="变异系数(CV)")
    total_qps = Column(Integer, nullable=False, comment="总QPS(每秒查询率)")
    record_time = Column(DateTime, nullable=False, comment="指标记录时间(从第三方采集的时间)")
    create_time = Column(DateTime, nullable=False, server_default=func.now(), comment="创建时间")
    update_time = Column(DateTime, nullable=False, server_default=func.now(), onupdate=func.now(), comment="更新时间")

    __table_args__ = (
        Index('idx_url_path', 'url_path'),
        Index('idx_record_time', 'record_time'),
        {'comment': '接口抖动率明细表'}
    )

    @classmethod
    def create_record(cls, url_path: int, url_name: str, coefficient_of_variation: float,
                     total_qps: int, record_time: datetime):
        """
        创建性能指标记录的便捷方法
        用于内部函数调用
        """
        return cls(
            url_path=url_path,
            url_name=url_name,
            coefficient_of_variation=coefficient_of_variation,
            total_qps=total_qps,
            record_time=record_time
        )