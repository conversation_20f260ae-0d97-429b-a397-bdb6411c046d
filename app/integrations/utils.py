import re
import os
import json
from app.integrations.tencent_cls.client import TencentCL<PERSON>lient

def normalize_path(path: str) -> str:
    """
    归一化path，去掉get参数，将数字参数替换为*，如 /api/classroom/35360481/message/send -> /api/classroom/*/message/send
    """
    if not path:
        return path
    # 去掉?及其后参数
    path = path.split('?')[0]
    # 替换数字为*
    path = re.sub(r'/\d+', '/*', path)
    return path 

def query_cls_results(query: str, start_time: int, end_time: int, limit: int = 100, return_all: bool = False) -> dict:
    """
    通用CLS SQL聚合查询，解析AnalysisResults

    :param query: CLS SQL查询语句
    :param start_time: 查询起始时间（时间戳，秒）
    :param end_time: 查询结束时间（时间戳，秒）
    :param limit: 查询结果限制条数
    :param return_all: 是否返回所有结果，True返回列表，False只返回第一条
    :return: 字典或字典列表
    """
    client = TencentCLSClient()
    resp = client.search_log(query, start_time, end_time, limit=limit)
    print(query, start_time, end_time)
    resp_json = json.loads(resp.to_json_string())
    results = []
    for item in resp_json.get("AnalysisResults", []):
        data = {d["Key"]: d["Value"] for d in item["Data"]}
        results.append(data)

    if return_all:
        return results
    else:
        return results[0] if results else {}

def query_cls_agg(query: str, start_time: int, end_time: int) -> dict:
    """
    通用CLS SQL聚合查询，返回AnalysisResults的key-value字典（仅取第一条）
    """
    return query_cls_results(query, start_time, end_time, limit=100, return_all=False)

def orm_to_dict_no_ctime(obj):
    """
    通用ORM转dict工具，去除 created_at 和 updated_at 字段。
    支持单个ORM对象或对象列表。
    """
    def _one(o):
        return {c.name: getattr(o, c.name) for c in o.__table__.columns if c.name not in ("created_at", "updated_at")}
    if isinstance(obj, list):
        return [_one(o) for o in obj]
    else:
        return _one(obj) 

def extract_app_from_pod(pod: str) -> str:
    """
    从pod名称中提取app名称，去掉eklet-前缀，匹配 main-weike-xxxx-xxxx 或 main-weike-tos-xxxx-xxxx
    """
    # 去掉eklet-前缀
    if pod.startswith("eklet-"):
        pod = pod[len("eklet-"):]
    # 匹配 main-weike-xxxx-xxxx 或 main-weike-tos-xxxx-xxxx
    m = re.match(r"([a-z0-9-]+)-[a-z0-9]+-[a-z0-9]+$", pod)
    if m:
        return m.group(1)
    return pod  # fallback 