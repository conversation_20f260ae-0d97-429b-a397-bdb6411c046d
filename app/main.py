from fastapi import FastAPI, Request
from app.core.scheduler import start_scheduler
from dotenv import load_dotenv
from app.models.peak_online_user import Base
from app.models.slow_api_detail import Base as SlowApiBase
from app.models.slow_api_agg import Base as SlowApiAggBase
from app.models.metric import Base as MetricBase
from app.models.slow_api_report import Base as SlowApiReportBase
from app.models.monitor import Base as PromMonitorBase
from app.models.grafana_metric_data import GrafanaCpuDataDetail, GrafanaMemDataDetail
from app.models.task_job import Base as TaskJobBase
from app.models.metric_calc import Base as MetricCalcBase, MetricCalcTaskORM, MetricCalcTaskResultORM
from app.models.metric_calc_result import Base as MetricCalcResultBase
from app.models.metric_calc_result_detail import Base as MetricCalcResultDetailBase
from app.models.core_interface_performance_detail import Base as CoreInterfacePerformanceDetail
from app.models.core_interface import Base as CoreInterface
from app.models.core_api_jitter_exception import Base as  CoreApiJitterException


from app.deps.db import engine, SessionLocal
from app.api.v1 import cls_metric as metric_router, monitor
from app.api.v1 import prometheus_metric as prometheus_metric_router
from scripts.init_prometheus import init_prometheus_cluster_data, init_metric_calc_data
from app.core.log import logger
import time
import logging
from sqlalchemy import inspect

load_dotenv()

app = FastAPI()

@app.middleware("http")
async def log_requests(request: Request, call_next):
    if not logger.isEnabledFor(logging.INFO):
        return await call_next(request)
    start_time = time.time()
    response = await call_next(request)
    process_time = (time.time() - start_time) * 1000
    logger.info(
        f"{request.method} {request.url.path} "
        f"params={dict(request.query_params)} "
        f"status={response.status_code} "
        f"time={process_time:.2f}ms"
    )
    return response

@app.on_event("startup")
def startup_event():
    # 自动建表
    Base.metadata.create_all(engine)
    SlowApiBase.metadata.create_all(engine)
    SlowApiAggBase.metadata.create_all(engine)
    MetricBase.metadata.create_all(engine)
    SlowApiReportBase.metadata.create_all(engine)
    PromMonitorBase.metadata.create_all(engine)
    GrafanaCpuDataDetail.metadata.create_all(engine)
    GrafanaMemDataDetail.metadata.create_all(engine)
    TaskJobBase.metadata.create_all(engine)
    MetricCalcBase.metadata.create_all(engine)
    MetricCalcResultBase.metadata.create_all(engine)
    MetricCalcResultDetailBase.metadata.create_all(engine)
    CoreInterfacePerformanceDetail.metadata.create_all(engine)
    CoreInterface.metadata.create_all(engine)
    CoreApiJitterException.metadata.create_all(engine)

    # 检查并创建指标计算任务表和结果表
    insp = inspect(engine)
    if not insp.has_table(MetricCalcTaskORM.__tablename__):
        MetricCalcTaskORM.__table__.create(bind=engine)
    if not insp.has_table(MetricCalcTaskResultORM.__tablename__):
        MetricCalcTaskResultORM.__table__.create(bind=engine)
    # 初始化prometheus集群和服务表基础数据
    with SessionLocal() as db:
        init_prometheus_cluster_data(db)
        init_metric_calc_data(db)
    start_scheduler()
    app.include_router(monitor.router, prefix="/api/v1")
    app.include_router(metric_router.router, prefix="/api/v1")
    app.include_router(prometheus_metric_router.router, prefix="/api/v1")

@app.get("/health_check")
def ping():
    return {"message": "pong"}

if __name__ == '__main__':
    import uvicorn
    uvicorn.run("app.main:app", host="0.0.0.0", port=8000, reload=True,env_file=".env")
